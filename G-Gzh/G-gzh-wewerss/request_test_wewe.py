import requests


def refreshArticles(mpId=None):
    """添加Feed数据"""
    # 请求URL
    url = "http://127.0.0.1:4000/trpc/feed.refreshArticles"
    
    # 请求参数
    params = {
        'batch': '1'
    }
    
    # 使用传入的数据或默认数据
    if mpId:
       data = {
            "0": {
                "mpId": mpId
            }
        }
    else:
        data = default_data
    
    # 请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
        'Accept': '*/*',
        'Authorization': '123567',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Cookie': '_xsrf=2|b72c8aa0|16ccec374bbb2f4a142fa319b3b5b3b9|1721460233; rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX18vSXMINUFmnxl1n%2Bq7bp1YDpLPHWDC4Lk%3D'
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, params=params, json=data, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        print(response.text)
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        # 打印更详细的错误信息
        if hasattr(e.response, 'text'):
            print(f"错误响应: {e.response.text}")
        return None


def addFeed(mp_data=None):
    """添加Feed数据"""
    # 请求URL
    url = "http://127.0.0.1:4000/trpc/feed.add"
    
    # 请求参数
    params = {
        'batch': '1'
    }
    
    # 使用传入的数据或默认数据
    if mp_data:
        # 确保传入的数据符合格式要求
        if not isinstance(mp_data, dict) or "0" not in mp_data:
            # 构造正确的数据格式
            data = {
                "0": {
                    "id": mp_data.get("id", ""),
                    "mpName": mp_data.get("mpName", ""),
                    "mpCover": mp_data.get("mpCover", ""),
                    "mpIntro": mp_data.get("mpIntro", ""),
                    "updateTime": mp_data.get("updateTime", 0),
                    "status": mp_data.get("status", 1)
                }
            }
        else:
            data = mp_data
    else:
        data = default_data
    
    # 请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
        'Accept': '*/*',
        'Authorization': '123567',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Cookie': '_xsrf=2|b72c8aa0|16ccec374bbb2f4a142fa319b3b5b3b9|1721460233; rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX18vSXMINUFmnxl1n%2Bq7bp1YDpLPHWDC4Lk%3D'
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, params=params, json=data, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        print(response.text)
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        # 打印更详细的错误信息
        if hasattr(e.response, 'text'):
            print(f"错误响应: {e.response.text}")
        return None



def getMpInfo(article_url=None):
    # 请求URL
    url = "http://127.0.0.1:4000/trpc/platform.getMpInfo"
    
    # 请求参数
    params = {
        'batch': '1'
    }
    
    # 请求体数据
    data = {
        "0": {
            "wxsLink": article_url or "https://mp.weixin.qq.com/s/btMqo6xPB8wLaf3ZrAkS0g"
        }
    }
    
    # 请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
        'Accept': '*/*',
        'Authorization': '123567',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Cookie': '_xsrf=2|b72c8aa0|16ccec374bbb2f4a142fa319b3b5b3b9|1721460233; rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX18vSXMINUFmnxl1n%2Bq7bp1YDpLPHWDC4Lk%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX19XNtFxPnMu3MKnmrnYRSJog6V5FCQ%2FFmI%3D; __clerk_db_jwt_RbibwK05=dvb_2oep7jsdSioVWrdPxrbnSaCr7k1; __refresh_RbibwK05=aS3qtPLLe7KR97AOxXRu; __client_uat_RbibwK05=0; _ga=GA1.1.725044992.1733107325; _ga_undefined=GS1.1.1735025595.5.0.1735025714.0.0.0; _ga_VSQK1TRMP6=GS1.1.1735522653.8.1.1735524808.0.0.615844244; __client_uat_YXNd1HkB=0; __clerk_db_jwt=dvb_2qyEoVBM38EhcwvcqS2ij2fiyVA; __clerk_db_jwt_-eCIQoih=dvb_2qyEoVBM38EhcwvcqS2ij2fiyVA; __refresh_-eCIQoih=bJ1KwSyyMfoK4vgLkz5M; __session_-eCIQoih=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18ycVI1bnlqYVJhenVRbmlxWDVmanR6TmJxc2wiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Qn-NhhuepaTlb7T9ImhlpPvyRscF9pv69hYtU6Kq61m3oQxU5FGJROc5sEa_FeahCjydCCt7LmqizgdX_OMlesDjHFGVButMaAw4n5-a7KYC3UI_2VDahOODqJ_VVUttgQedE1EvkpK8RGLSTk9exTXgffh73TKZ4AhDFud2l7KGslD4JamEZQ1PDSSwOoT-YRzY0hQFvU7ojGuESSTRO4EqRbXNii87Fble6sV6Ti84CxJ0xvaLSAvsn72PpE97YGPgpi6kJibQpUoe2OkyVb7oPW4yAInkwppkZkDLqFv3GB4Kn-ob3MQVWHPONStW6xrmua5SmbH0Uq_Pb2OANw; __session=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18ycVI1bnlqYVJhenVRbmlxWDVmanR6TmJxc2wiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Qn-NhhuepaTlb7T9ImhlpPvyRscF9pv69hYtU6Kq61m3oQxU5FGJROc5sEa_FeahCjydCCt7LmqizgdX_OMlesDjHFGVButMaAw4n5-a7KYC3UI_2VDahOODqJ_VVUttgQedE1EvkpK8RGLSTk9exTXgffh73TKZ4AhDFud2l7KGslD4JamEZQ1PDSSwOoT-YRzY0hQFvU7ojGuESSTRO4EqRbXNii87Fble6sV6Ti84CxJ0xvaLSAvsn72PpE97YGPgpi6kJibQpUoe2OkyVb7oPW4yAInkwppkZkDLqFv3GB4Kn-ob3MQVWHPONStW6xrmua5SmbH0Uq_Pb2OANw; __client_uat_-eCIQoih=1735627370; __client_uat=1735627370; authjs.csrf-token=933c581ab428329d1e30e1349df9ba693229976150341f0b8e58e5b9e7601a0f%7C0e0a988d03e78519572d1b1d0d77521d253245abc54da4e869e07e632b8147f9; authjs.callback-url=http%3A%2F%2Flocalhost%3A3000; __stripe_mid=73e88b43-1776-4641-8483-1c4d77aa94f59ad414; _ga_XXXXXXXX=GS1.1.1736490423.1.1.1736490517.0.0.0; NEXT_LOCALE=zh; next-auth.csrf-token=4d4d977f1f89756d1e8c8a08ca5caa924cdd1f7d54aee46652aef76baa7296f3%7Cdae55705962688e53a90cbeb9975b1153e18a2ddc25ada1749aa818d3ac230ce; next-auth.callback-url=http%3A%2F%2Flocalhost%3A3000; _ga_9ST4TEHF2S=GS1.1.1738822916.4.1.1738825880.0.0.0; _ga_6K0QD0PJSZ=GS1.1.1739328266.1.1.1739328349.0.0.0; _ga_J2LXNKX4WT=GS1.1.1739328375.1.1.1739329100.0.0.0; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX19v3mebpnEEL8NFjR8X9MINT3pwglRfWjW5cSLC4RLPLK4lm2iXAejaD%2F%2FqJZ7kE7c300dE%2FvE0Hg%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX19j4vhxz2aiKd42Ay9WBWBPCaROvoCtb0gR1%2FOt8tL41oJMBVbCyNOyIavaEZiGyYaIgNkJpemHS6q2hrTOEgMDpnCUlRtQMI9r76zOw2vbBe1W28%2Fug3RzkikdATzWLOhL9eeRV72OoeW4ZeHmANdfFbciDpIDAVg%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX1%2FF5Wf7pul6QsWGpzwaH3NodWVPJFAcA9cUKzx2mWNCNgpw1ydWw%2BdtFLO52%2BZqfVlg4qw7upHBL9fhd4LR7H76XahKHR3Fsf8yS%2F9URvZ2w2H%2BTGb40v5aLPSO5GKGF5M4ZRXNGd%2BKXA%3D%3D; rl_session=RudderEncrypt%3AU2FsdGVkX18vkhP3VkvJiP6pU7080DjAyknRiJnUF9%2BQsCPuA3droJKDL2yzhsiZ8ZfvFW5FDTYGsVQrMxhs%2FQHtZFFVNdfv19apzbVkA9gUJ4knOAEk47iSoBf7on94g6kNX2W4tEDYtwbVAjFPqw%3D%3D; ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog=%7B%22distinct_id%22%3A%2282bacf3e5291fb5e6416520dee19bc536867df2518647dbe9ff09480fe6e5855%23c75bffca-4de5-46f6-802e-91f3f6fbd5a9%22%2C%22%24sesid%22%3A%5Bnull%2Cnull%2Cnull%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2F127.0.0.1%3A5678%2Fsetup%22%7D%7D'  # 需要替换为实际的cookie值
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, params=params, json=data, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        data=response.json()[0]['result']['data'][0]
        return data
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None



def getFeedList():
    """获取Feed列表数据"""
    # 请求URL
    url = "http://127.0.0.1:4000/trpc/feed.list"
    
    # 请求参数
    params = {
        'batch': '1',
        'input': '{"0":{}}'  # URL编码的JSON字符串
    }
    
    # 请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
        'Accept': '*/*',
        'Authorization': '123567',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Cookie': '_xsrf=2|b72c8aa0|16ccec374bbb2f4a142fa319b3b5b3b9|1721460233; rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX18vSXMINUFmnxl1n%2Bq7bp1YDpLPHWDC4Lk%3D'
    }
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        # 打印更详细的错误信息
        if hasattr(e.response, 'text'):
            print(f"错误响应: {e.response.text}")
        return None

if __name__ == "__main__":
    # 测试文章URL
    article_url = "https://mp.weixin.qq.com/s/btMqo6xPB8wLaf3ZrAkS0g"
    article_url = "https://mp.weixin.qq.com/s/GU_q084NEPMK1rCGoA4jxw"
    data = getMpInfo(article_url)
    if data is None: 
        print("获取文章信息失败") 
        return
    result=addFeed(data)
    if result:
        mpId=result[0]['result']['data']['id']
        # 测试刷新文章
        refresh_result = refreshArticles(mpId)
        if refresh_result:
            print(refresh_result)

    # refreshArticles('MP_WXS_3923498094')
    print(getFeedList())
