import time
import psutil
import pyautogui
import os
from time import sleep
import pyperclip

def GetWeChatPID(name):
    try:
        pids = psutil.process_iter()
        for pid in pids:
            if pid.name() == name:
                return pid.pid
        return None
    except Exception as e:
        print(f'获取微信进程失败: {str(e)}')
        return None
    
def check_wechat_window():
    # 检查微信是否运行
    pid = GetWeChatPID('WeChat')
    if pid is None:
        print('请先打开微信应用')
        return False
    return True

def get_last_message():
    # 复制最后一条消息
    pyautogui.hotkey('command', 'a')  # 全选
    time.sleep(0.5)
    pyautogui.hotkey('command', 'c')  # 复制
    time.sleep(0.5)
    last_message = pyperclip.paste()  # 从剪贴板获取内容
    return last_message

def auto_play_wechat(url):
    try:
        # 检查微信窗口状态
        if not check_wechat_window():
            return
            
        # 查找并激活微信窗口
        # 使用AppleScript激活微信窗口
        os.system("osascript -e 'tell application \"WeChat\" to activate'")
        time.sleep(1)  # 给窗口切换更多时间
        
        # 模拟搜索操作
        pyautogui.hotkey('command', 'f')  # MacOS上的搜索快捷键
        time.sleep(1)  # 增加延迟时间
        
        # 输入文件传输助手
        pyautogui.write('文件传输助手', interval=0.1)  # 添加输入间隔
        time.sleep(1)  # 增加等待时间
        pyautogui.press('enter')
        time.sleep(1)  # 增加等待时间
        time.sleep(0.5)
        
        # 输入URL
        pyautogui.write(url)
        time.sleep(0.5)
        pyautogui.press('enter')
        time.sleep(1)  # 等待消息发送
        
        # 获取最近的聊天信息
        last_message = get_last_message()
        print('最近的聊天信息:', last_message)
        
        # 最小化窗口
        pyautogui.hotkey('command', 'm')
        print('操作完成')
        
        return last_message
        
    except Exception as e:
        print(f'自动操作失败: {str(e)}')
        return None

if __name__ == '__main__':
    # 测试URL
    test_url = 'https://mp.weixin.qq.com/s/R5Y08wm1Fx8-hrCC2pMAng'
    auto_play_wechat(test_url)