import requests
import feedparser
import json
from urllib.parse import parse_qs, urlparse
import re
from bs4 import BeautifulSoup
import os
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
current_dir = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(current_dir, 'config.json')
wechat_data_path = os.path.join(current_dir, 'wechat_data.json')
processed_wechat_data_path = os.path.join(current_dir, 'processed_wechat_data.json')


class WechatSpider:
    def __init__(self, base_rss_url=None):
        # 从config.json加载cookie配置
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                appmsg_cookie = config.get('cookies', '')
        except Exception as e:
            print(f'读取config.json失败: {e}')
            appmsg_cookie = ''

        self.headers = {
            "User-Agent": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0Chrome/57.0.2987.132 MQQBrowser/6.2 Mobile",
            "Cookie": appmsg_cookie,
        }
        self.cookies = appmsg_cookie
        self.base_rss_url = base_rss_url or 'http://127.0.0.1:4000/feeds/MP_WXS_3236757533.atom'
        self.appmsg_token = ''

    def extract_author_id(self, article_url):
        """从文章URL中提取作者ID"""
        try:
            parsed_url = urlparse(article_url)
            query_params = parse_qs(parsed_url.query)
            biz = query_params.get('__biz', [None])[0]
            return f'MP_WXS_{biz}' if biz else None
        except Exception as e:
            print(f'提取作者ID失败: {e}')
            return None

    def get_rss_feed(self):
        """获取RSS源内容"""
        try:
            feed = feedparser.parse(self.base_rss_url)
            articles = []
            for entry in feed.entries:
                article_link = entry.link if hasattr(entry, 'link') else None
                if article_link:
                    articles.append({
                        'title': entry.title if hasattr(entry, 'title') else '',
                        'link': article_link,
                        'published': entry.published if hasattr(entry, 'published') else '',
                        'author': entry.author if hasattr(entry, 'author') else ''
                    })
            return articles
        except Exception as e:
            print(f'获取RSS源失败: {e}')
            return []

    def __get_params(self, article_url):
        """
        解析文章url, 获取必要的请求参数

        Parameters
        ----------
        article_url: str
            文章链接

        Returns
        -------
        (str, str, str, str):
            __biz, mid, idx, sn
        """

        self.__verify_url(article_url)
        string_lst = article_url.split("?")[1].split("&")
        dict_value = [string[string.index("=") + 1:] for string in string_lst]
        __biz, mid, idx, sn, *_ = dict_value
        sn = sn[:-3] if sn[-3] == "#" else sn
        return __biz, mid, idx, sn

    def __verify_url(self, article_url):
        """
        简单验证文章url是否符合要求

        Parameters
        ----------
        article_url: str
            文章链接
        """
        verify_lst = ["mp.weixin.qq.com", "__biz", "mid", "sn", "idx"]
        for string in verify_lst:
            if string not in article_url:
                raise Exception("params is error, please check your article_url")

    def extract_params_from_url(self, url):
        """从URL中提取参数"""
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        # 将列表值转换为单个值
        return {k: v[0] for k, v in query_params.items()}

    def parse_article_data(self, html_content):
        """解析文章中的各种数据"""
        try:
            data = {
                'read_num': -1,
                'like_num': 0,
                'old_like_num': 0,
                'share_num': 0
            }
            
            # 解析阅读量
            read_num_match = re.search(r"var read_num\s*=\s*['\"]([\d]+)['\"]\s*\*\s*1;", html_content)
            read_num_new_match = re.search(r"var read_num_new\s*=\s*['\"]([\d]+)['\"]\s*\*\s*1;", html_content)
            
            if read_num_new_match and read_num_new_match.group(1):
                try:
                    data['read_num'] = int(read_num_new_match.group(1))
                except (ValueError, TypeError):
                    print(f'解析新版阅读量失败')
            elif read_num_match and read_num_match.group(1):
                try:
                    data['read_num'] = int(read_num_match.group(1))
                except (ValueError, TypeError):
                    print(f'解析旧版阅读量失败')

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 解析点赞数 - 新版点赞
            like_btn = soup.find('span', id='js_bar_like_btn')
            if like_btn:
                like_text = like_btn.get_text(strip=True)
                try:
                    data['like_num'] = int(like_text) if like_text.isdigit() else 0
                except (ValueError, TypeError):
                    print(f'解析点赞数失败')
                
            # 解析旧版点赞数
            old_like_btn = soup.find('span', id='js_bar_oldlike_btn')
            if old_like_btn:
                old_like_text = old_like_btn.get_text(strip=True)
                try:
                    data['old_like_num'] = int(old_like_text) if old_like_text.isdigit() else 0
                except (ValueError, TypeError):
                    print(f'解析旧版点赞数失败')
                
            # 解析分享数
            share_btn = soup.find('span', id='js_bar_share_btn')
            if share_btn:
                share_text = share_btn.get_text(strip=True)
                try:
                    data['share_num'] = int(share_text) if share_text.isdigit() else 0
                except (ValueError, TypeError):
                    print(f'解析分享数失败')
                
            return data

        except Exception as e:
            print(f'解析文章数据失败: {str(e)}')
            return {
                'read_num': -1,
                'like_num': 0,
                'old_like_num': 0,
                'share_num': 0
            }

    def get_article_stats(self, article_url):
        """获取文章阅读数据"""
        try:
            # 从URL中提取参数
            params = self.extract_params_from_url(article_url)
            if not params:
                return {
                    'status': 'error',
                    'message': '无法从URL中提取参数'
                }
            
            # 发送请求
            response = requests.get(
                article_url,
                headers=self.headers,
                verify=False  # 忽略SSL证书验证
            )
            
            # 检查响应状态
            if response.status_code == 200:
                # 解析文章数据
                article_data = self.parse_article_data(response.text)
                
                # 检查是否成功获取阅读量
                if article_data['read_num'] == -1:
                    # 初始化重试计数器（如果不存在）
                    print(f"阅读量获取失败： 文章URL: {article_url}")
                    if article_data['old_like_num'] == 0:
                        return {
                          'status': 'error',
                          'message': '无法获取文章阅读数据'
                        }

                
                print(f"文章阅读量: {article_data['read_num']}")
                print(f"收藏数: {article_data['like_num']}")
                print(f"点赞数: {article_data['old_like_num']}")
                print(f"分享数: {article_data['share_num']}")
                
                return {
                    'status': 'success',
                    'data': article_data
                }
            else:
                return {
                    'status': 'error',
                    'message': f'请求失败，状态码：{response.status_code}'
                }
                
        except Exception as e:
            print(f'发生错误：{str(e)}')
            return {
                'status': 'error',
                'message': f'发生错误：{str(e)}'
            }

    def save_data(self, data, filename=wechat_data_path):
        """保存数据到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f'数据已保存到 {filename}')
        except Exception as e:
            print(f'保存数据失败: {e}')

    def process_articles(self):
        """处理RSS源中的所有文章"""
        try:
            # 1. 获取RSS源中的所有文章
            articles = self.get_rss_feed()
            if not articles:
                print('未获取到任何文章')
                return None
            
            # 2. 处理文章列表
            results = []
            for article in articles:
                article_data = {
                    'title': article['title'],
                    'link': article['link'],
                    'published': article['published'],
                    'author': article['author'],
                    # 'read_count': self.get_article_stats(article['link'])
                }
                results.append(article_data)
                print(f'处理文章: {article["title"]}')

            # 3. 保存数据
            self.save_data(results)
            return results

        except Exception as e:
            print(f'处理文章失败: {e}')
            return None

    def read_json_links(self, json_file=wechat_data_path):
        """从JSON文件读取文章链接"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    return [item.get('link') for item in data if item.get('link')]
                return []
        except Exception as e:
            print(f'读取JSON文件失败: {e}')
            return []

    def process_json_articles(self, json_file=wechat_data_path):
        """处理JSON文件中的文章链接"""
        try:
            # 1. 读取JSON文件中的链接
            links = self.read_json_links(json_file)
            if not links:
                print('未从JSON文件获取到任何文章链接')
                return None
            
            # 2. 处理文章列表
            results = []
            for link in links:
                try:
                    # 解析文章链接获取真实URL
                    parsed_link = self.parse_weixin_article(link)
                    if parsed_link:
                        # 获取文章阅读数据
                        stats = self.get_article_stats(parsed_link)
                        # 根据stats的状态打印不同的信息
                        if stats['status'] == 'success':
                            read_num = stats['data']['read_num']
                            print(f'处理文章链接成功: {link}, 阅读量: {read_num}')
                        else:
                            print(f'获取文章数据失败: {link}, 原因: {stats["message"]}')
                            break
                        
                        article_data = {
                            'original_link': link,
                            'parsed_link': parsed_link,
                            'data': stats['data']
                        }
                        results.append(article_data)                     
                    else:
                        print(f'解析文章链接失败: {link}')
                except Exception as e:
                    print(f'处理文章链接异常: {link}, 错误: {str(e)}')
                    continue

            # 3. 保存数据
            if results:
                self.save_data(results, processed_wechat_data_path)
                print(f'成功处理 {len(results)} 篇文章')
            else:
                print('没有成功处理任何文章')
            return results

        except Exception as e:
            print(f'处理JSON文件文章失败: {e}')
            return None

    def parse_weixin_article(self,url):
        """解析公众号文章链接获取文章的biz、mid、idx、sn等参数"""
        try:
            response = requests.get(url, verify=False, headers=self.headers)
            html_content = response.text
            
            # 使用多个正则表达式模式来增加匹配成功率
            patterns = [
                # 模式1: 标准格式
                r'reportOpt:\s*{\s*uin:\s*\'[^\']*\',\s*biz:\s*"([^"]+)"\s*\|\|\s*"",\s*mid:\s*"([^"]+)"\s*\|\|\s*""\s*\|\|\s*"",\s*idx:\s*"([^"]+)"\s*\|\|\s*""\s*\|\|\s*"",\s*sn:\s*"([^"]+)"\s*\|\|\s*""\s*\|\|\s*""',
                # 模式2: 简化格式
                r'var\s+biz\s*=\s*"([^"]+)".*?var\s+mid\s*=\s*"([^"]+)".*?var\s+idx\s*=\s*"([^"]+)".*?var\s+sn\s*=\s*"([^"]+)"',
                # 模式3: URL参数格式
                r'__biz=([^&]+).*?mid=([^&]+).*?idx=([^&]+).*?sn=([^&]+)'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, html_content, re.DOTALL)
                if match:
                    biz, mid, idx, sn = match.groups()
                    url = f'https://mp.weixin.qq.com/s?__biz={biz}&mid={mid}&idx={idx}&sn={sn}'
                    return url
            
            print("未找到匹配数据，请检查页面结构或更新匹配模式")
            return None
            
        except Exception as e:
            print(f"解析文章链接失败: {str(e)}")
            return None
    
def main():
    """
    1. cookies只能适用于已经访问过公众号的历史文章;
    2. 期望随便获取的cookie，可以解析任意公众号的文章数据;
    """
    # spider = WechatSpider()
    # spider = WechatSpider('http://127.0.0.1:4000/feeds/MP_WXS_3236757533.atom')
    # spider = WechatSpider('http://127.0.0.1:4000/feeds/MP_WXS_3094060680.atom')
    # spider = WechatSpider('http://127.0.0.1:4000/feeds/MP_WXS_3861464941.atom')
    spider = WechatSpider('http://127.0.0.1:4000/feeds/MP_WXS_3240750598.atom')
    # spider = WechatSpider('http://127.0.0.1:4000/feeds/MP_WXS_3909569981.atom')
    # spider = WechatSpider('http://127.0.0.1:4000/feeds/MP_WXS_3913422171.atom')
    
    # 处理RSS源文章
    rss_results = spider.process_articles()
    if rss_results:
        print(f'成功采集RSS源 {len(rss_results)} 篇文章数据')
    
    # 处理JSON文件中的文章
    json_results = spider.process_json_articles()
    if json_results:
        print(f'成功处理JSON文件 {len(json_results)} 篇文章数据')
    
    

if __name__ == '__main__':
    main()