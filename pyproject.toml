[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "spider"
version = "0.1.0"
description = "Spider project with various tools and utilities"
readme = "README.md"
requires-python = ">=3.12.10"
license = {text = "MIT"}
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]

# Core dependencies
dependencies = [
    "agunua==1.7.1",
    "aiofiles==23.2.1",
    "aiohttp==3.9.1",
    "aiosignal==1.3.1",
    "annotated-types==0.6.0",
    "anthropic==0.25.8",
    "anyio==4.1.0",
    "APScheduler==3.10.4",
    "asttokens==2.4.1",
    "async-timeout==4.0.3",
    "attrs==23.1.0",
    "beautifulsoup4==4.12.2",
    "betterproto==1.2.5",
    "blinker==1.7.0",
    "Brotli==1.1.0",
    "bs4==0.0.1",
    "cachetools==5.3.3",
    "certifi==2023.11.17",
    "cffi==1.16.0",
    "cfgv==3.4.0",
    "chardet==5.2.0",
    "charset-normalizer==3.3.2",
    "claude-api-py==0.0.5",
    "click==8.1.7",
    "cryptography==41.0.7",
    "cssselect==1.2.0",
    "decorator==4.4.2",
    "deep-translator==1.11.4",
    "Deprecated==1.2.14",
    "distlib==0.3.7",
    "distro==1.9.0",
    "docx==0.2.4",
    "et-xmlfile==1.1.0",
    "exceptiongroup==1.2.0",
    "executing==2.0.1",
    "feedfinder2==0.0.4",
    "feedparser==6.0.11",
    "filelock==3.13.1",
    "Flask==3.0.0",
    "Flask-Cors==4.0.0",
    "frozenlist==1.4.0",
    "fsspec==2024.3.1",
    "gitdb==4.0.11",
    "GitPython==3.1.43",
    "google-ai-generativelanguage==0.6.2",
    "google-api-core==2.18.0",
    "google-api-python-client==2.126.0",
    "google-auth==2.29.0",
    "google-auth-httplib2==0.2.0",
    "google-auth-oauthlib==1.2.0",
    "google-generativeai==0.5.2",
    "googleapis-common-protos==1.63.0",
    "groq==0.5.0",
    "grpcio==1.63.0",
    "grpcio-status==1.62.2",
    "grpclib==0.4.7",
    "gTTS==2.5.1",
    "h11==0.14.0",
    "h2==4.1.0",
    "hpack==4.0.0",
    "html2text==2024.2.26",
    "HTMLParser==0.0.2",
    "httpcore==1.0.2",
    "httplib2==0.22.0",
    "httpx==0.25.2",
    "huggingface-hub==0.23.0",
    "Hypercorn==0.16.0",
    "hyperframe==6.0.1",
    "identify==2.5.32",
    "idna==3.6",
    "imageio==2.34.0",
    "imageio-ffmpeg==0.4.9",
    "ipython==8.24.0",
    "itsdangerous==2.1.2",
    "jedi==0.19.1",
    "jieba3k==0.35.1",
    "Jinja2==3.1.2",
    "joblib==1.4.0",
    "libretranslatepy==2.1.1",
    "lxml==4.9.3",
    "Markdown==3.6",
    "markdown-it-py==3.0.0",
    "markdowntodocx==*******",
    "MarkupSafe==2.1.3",
    "matplotlib-inline==0.1.7",
    "mdurl==0.1.2",
    "moviepy==1.0.3",
    "mutagen==1.47.0",
    "netaddr==1.2.1",
    "newspaper3k==0.2.8",
    "nltk==3.8.1",
    "nodeenv==1.8.0",
    "notion-client==2.1.0",
    "numpy==1.26.4",
    "oauthlib==3.2.2",
    "openai==1.30.1",
    "opencv-python==********",
    "opengraph_py3==0.71",
    "openpyxl==3.1.2",
    "Pillow>=10.0.0",
    "outcome==1.3.0.post0",
    "packaging==24.0",
    "parso==0.8.4",
    "pexpect==4.9.0",
    "phidata==2.4.10",
    "ping3==4.0.4",
    "platformdirs==4.1.0",
    "pre-commit==3.5.0",
    "priority==2.0.0",
    "proglog==0.1.10",
    "prompt-toolkit==3.0.43",
    "proto-plus==1.23.0",
    "protobuf==4.25.3",
    "ptyprocess==0.7.0",
    "pure-eval==0.2.2",
    "pyasn1==0.6.0",
    "pyasn1_modules==0.4.0",
    "pycparser==2.21",
    "pycryptodomex==3.19.1",
    "pydantic==2.7.1",
    "pydantic-settings==2.2.1",
    "pydantic_core==2.18.2",
    "pyee==11.1.0",
    "PyGithub==2.1.1",
    "Pygments==2.18.0",
    "PyJWT==2.8.0",
    "PyNaCl==1.5.0",
    "pyOpenSSL==24.1.0",
    "pyparsing==3.1.2",
    "pypng==0.20220715.0",
    "PyQRCode==1.2.1",
    "PySocks==1.7.1",
    "python-dateutil==2.8.2",
    "python-docx==1.1.0",
    "python-dotenv==1.0.1",
    "python-json-logger==2.0.7",
    "pyttsx3==2.90",
    "pytube==15.0.0",
    "pytz==2023.3.post1",
    "PyYAML==6.0.1",
    "qrcode==7.4.2",
    "Quart==0.19.4",
    "quart-cors==0.7.0",
    "regex==2024.4.16",
    "requests==2.31.0",
    "requests-file==2.0.0",
    "requests-oauthlib==1.3.1",
    "rich==13.7.1",
    "rsa==4.9",
    "schedule==1.2.1",
    "selenium==4.16.0",
    "sgmllib3k==1.0.0",
    "shellingham==1.5.4",
    "six==1.16.0",
    "smmap==5.0.1",
    "sniffio==1.3.0",
    "sortedcontainers==2.4.0",
    "soupsieve==2.5",
    "SQLAlchemy==2.0.25",
    "sseclient-py==1.8.0",
    "stack-data==0.6.3",
    "stringcase==1.2.0",
    "taskgroup==0.0.0a4",
    "tinysegmenter==0.3",
    "tldextract==5.1.2",
    "tokenizers==0.19.1",
    "tomli==2.0.1",
    "tqdm==4.66.1",
    "traitlets==5.14.3",
    "translate==3.6.1",
    "trio==0.23.2",
    "trio-websocket==0.11.1",
    "typer==0.12.3",
    "typing_extensions==4.8.0",
    "tzlocal==5.2",
    "uritemplate==4.1.1",
    "urllib3==2.1.0",
    "virtualenv==20.25.0",
    "wcwidth==0.2.13",
    "websockets==12.0",
    "wechaty==0.10.7",
    "wechaty-grpc==0.20.19",
    "wechaty-puppet==0.4.23",
    "wechaty-puppet-service==0.8.10",
    "Werkzeug==3.0.1",
    "wrapt==1.16.0",
    "wsproto==1.2.0",
    "yarl==1.9.3",
    "yt-dlp==2025.04.30",
    "playwright>=1.44.0"
]

[project.optional-dependencies]
dev = [
    "pre-commit>=3.5.0",
    "ipython>=8.24.0",
    "pytest>=7.0.0",
]

mac = [
    "pyobjc>=10.2",
]

nlp = [
    "nltk>=3.8.1",
    "newspaper3k>=0.2.8",
]

[tool.setuptools]
packages = ["spider"]

[tool.black]
line-length = 88
target-version = ["py38", "py39", "py310", "py311"]

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest]
testpaths = ["tests"]
python_files = "test_*.py"
